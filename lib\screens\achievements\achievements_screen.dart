import 'package:flutter/material.dart';
import '../../widgets/custom_app_bar.dart';
import '../../theme/app_theme.dart';
import '../../models/achievement.dart';
import '../../services/achievement_service.dart';

class AchievementsScreen extends StatefulWidget {
  const AchievementsScreen({super.key});

  @override
  State<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends State<AchievementsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  AchievementSummary? _summary;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    setState(() {
      _summary = AchievementService.getAchievementSummary();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Achievements',
      ),
      body: Column(
        children: [
          // Summary header
          if (_summary != null) _buildSummaryHeader(),
          
          // Tab bar
          TabBar(
            controller: _tabController,
            isScrollable: true,
            tabs: [
              Tab(text: 'All (${_summary?.totalAchievements ?? 0})'),
              Tab(text: '💰 Savings'),
              Tab(text: '📱 Trials'),
              Tab(text: '🔥 Streaks'),
              Tab(text: '🏆 Milestones'),
            ],
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAchievementsList(AchievementService.getAllAchievements()),
                _buildAchievementsList(AchievementService.getAchievementsByCategory(AchievementCategory.savings)),
                _buildAchievementsList(AchievementService.getAchievementsByCategory(AchievementCategory.trials)),
                _buildAchievementsList(AchievementService.getAchievementsByCategory(AchievementCategory.streaks)),
                _buildAchievementsList(AchievementService.getAchievementsByCategory(AchievementCategory.milestones)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryHeader() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.celebrationGold.withOpacity(0.1),
            AppTheme.primaryGreen.withOpacity(0.05),
          ],
        ),
      ),
      child: Column(
        children: [
          // Progress circle
          SizedBox(
            width: 120,
            height: 120,
            child: Stack(
              children: [
                // Background circle
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    value: 1.0,
                    strokeWidth: 8,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade200),
                  ),
                ),
                // Progress circle
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    value: _summary!.completionPercentage / 100,
                    strokeWidth: 8,
                    backgroundColor: Colors.transparent,
                    valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.celebrationGold),
                  ),
                ),
                // Center content
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${_summary!.completionPercentage.toStringAsFixed(0)}%',
                        style: AppTextStyles.heading2.copyWith(
                          color: AppTheme.celebrationGold,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Complete',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Stats row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(
                '${_summary!.unlockedAchievements}/${_summary!.totalAchievements}',
                'Unlocked',
                Icons.emoji_events,
              ),
              _buildStatItem(
                '${_summary!.totalPoints}',
                'Points',
                Icons.star,
              ),
              _buildStatItem(
                '${_summary!.currentStreak}',
                'Day Streak',
                Icons.local_fire_department,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String value, String label, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppTheme.celebrationGold,
          size: 24,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: AppTextStyles.heading3.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementsList(List<Achievement> achievements) {
    if (achievements.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.emoji_events,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                'No Achievements',
                style: AppTextStyles.heading3.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(
                'Keep using the app to unlock achievements!',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        final achievement = achievements[index];
        return _buildAchievementCard(achievement);
      },
    );
  }

  Widget _buildAchievementCard(Achievement achievement) {
    final isUnlocked = achievement.isUnlocked;
    
    return Opacity(
      opacity: isUnlocked ? 1.0 : 0.6,
      child: Card(
        margin: const EdgeInsets.only(bottom: AppSpacing.md),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Row(
            children: [
            // Achievement icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: isUnlocked 
                    ? AppTheme.celebrationGold.withOpacity(0.2)
                    : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(AppBorderRadius.circular),
              ),
              child: Icon(
                _getIconData(achievement.iconName),
                size: 30,
                color: isUnlocked 
                    ? AppTheme.celebrationGold
                    : Colors.grey.shade400,
              ),
            ),
            
            const SizedBox(width: AppSpacing.md),
            
            // Achievement info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          achievement.title,
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isUnlocked ? null : Colors.grey.shade600,
                          ),
                        ),
                      ),
                      if (isUnlocked)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.sm,
                            vertical: AppSpacing.xs,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryGreen.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                          ),
                          child: Text(
                            '+${achievement.rewardPoints}',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppTheme.primaryGreen,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.xs),
                  
                  Text(
                    achievement.description,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: isUnlocked ? AppTheme.textSecondary : Colors.grey.shade500,
                    ),
                  ),
                  
                  if (!isUnlocked && achievement.progress > 0) ...[
                    const SizedBox(height: AppSpacing.sm),
                    LinearProgressIndicator(
                      value: achievement.progress,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      '${achievement.currentValue}/${achievement.targetValue}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                  
                  if (isUnlocked && achievement.unlockedAt != null) ...[
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      'Unlocked ${_formatDate(achievement.unlockedAt!)}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppTheme.successGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'savings':
        return Icons.savings;
      case 'attach_money':
        return Icons.attach_money;
      case 'account_balance_wallet':
        return Icons.account_balance_wallet;
      case 'star':
        return Icons.star;
      case 'add_circle':
        return Icons.add_circle;
      case 'subscriptions':
        return Icons.subscriptions;
      case 'workspace_premium':
        return Icons.workspace_premium;
      case 'local_fire_department':
        return Icons.local_fire_department;
      case 'whatshot':
        return Icons.whatshot;
      case 'emoji_events':
        return Icons.emoji_events;
      case 'celebration':
        return Icons.celebration;
      default:
        return Icons.emoji_events;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'today';
    } else if (difference == 1) {
      return 'yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
